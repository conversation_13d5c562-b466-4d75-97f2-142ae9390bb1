#!/bin/bash
REPO_OWNER="arendai"
REPO_NAME="harmony-simulation"

# Fetch all artifact IDs
artifact_ids=$(gh api "repos/$REPO_OWNER/$REPO_NAME/actions/artifacts" | jq -r '.artifacts[].id')

# Check if artifact_ids is empty
if [ -z "$artifact_ids" ]; then
  echo "No artifacts found to delete."
  exit 0
fi

# Loop through and delete each artifact, forcing line-by-line splitting
echo "$artifact_ids" | while read -r id; do
  echo "Deleting artifact ID: $id"
  gh api -X DELETE "repos/$REPO_OWNER/$REPO_NAME/actions/artifacts/$id"
done
