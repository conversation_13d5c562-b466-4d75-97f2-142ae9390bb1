#!/usr/bin/env python3
"""
Test tag parsing functionality
"""

import sys
import os

# Add the current directory to the path so we can import gh_packages
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gh_packages import GitHubPackageManager, Colors

def test_tag_parsing():
    """Test tag parsing with mock data"""
    print(f"{Colors.BLUE}Testing tag parsing functionality...{Colors.NC}")
    
    # Mock version data similar to what GitHub API returns
    mock_versions = [
        {
            "id": 460976419,
            "name": "sha256:3626ed25ee001c0b111b93c0b8c154ff71165ecabf40129422455f98d8a207a7",
            "created_at": "2025-07-14T14:25:00Z",
            "metadata": {
                "package_type": "container",
                "container": {
                    "tags": []  # Untagged
                }
            }
        },
        {
            "id": 479602138,
            "name": "sha256:abcd1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab",
            "created_at": "2025-07-15T10:30:00Z",
            "metadata": {
                "package_type": "container",
                "container": {
                    "tags": ["v1.0.0", "latest"]  # Tagged
                }
            }
        },
        {
            "id": 480000000,
            "name": "sha256:efgh5678901234567890efgh5678901234567890efgh5678901234567890efgh",
            "created_at": "2025-07-16T15:45:00Z",
            "metadata": {
                "package_type": "container",
                "container": {
                    "tags": ["develop"]  # Tagged with develop
                }
            }
        },
        {
            "id": 490000000,
            "name": "regular-name-not-sha256",
            "created_at": "2025-07-17T09:15:00Z",
            "metadata": {
                "package_type": "container",
                "container": {
                    "tags": ["v2.0.0"]
                }
            }
        }
    ]
    
    # Test filtering logic
    sha256_versions = []
    for version in mock_versions:
        if version['name'].startswith('sha256:'):
            # Check if version has tags
            tags = []
            if 'metadata' in version and 'container' in version['metadata']:
                tags = version['metadata']['container'].get('tags', [])
            
            # Add tag information to the version object
            version['tags'] = tags
            version['has_tags'] = len(tags) > 0
            sha256_versions.append(version)
    
    print(f"\n{Colors.YELLOW}Mock data analysis:{Colors.NC}")
    print(f"Total versions: {len(mock_versions)}")
    print(f"SHA256 versions: {len(sha256_versions)}")
    
    tagged_versions = [v for v in sha256_versions if v['has_tags']]
    untagged_versions = [v for v in sha256_versions if not v['has_tags']]
    
    print(f"Tagged SHA256 versions: {len(tagged_versions)}")
    print(f"Untagged SHA256 versions: {len(untagged_versions)}")
    
    print(f"\n{Colors.GREEN}Tagged versions (SHOULD NOT be deleted by default):{Colors.NC}")
    for version in tagged_versions:
        tags_str = ', '.join(version['tags'])
        print(f"  ID: {version['id']}, Tags: {tags_str}")
    
    print(f"\n{Colors.YELLOW}Untagged versions (safe to delete):{Colors.NC}")
    for version in untagged_versions:
        print(f"  ID: {version['id']}, Tags: (none)")
    
    # Verify the logic
    assert len(sha256_versions) == 3, "Should find 3 sha256 versions"
    assert len(tagged_versions) == 2, "Should find 2 tagged versions"
    assert len(untagged_versions) == 1, "Should find 1 untagged version"
    
    print(f"\n{Colors.GREEN}✓ All tag parsing tests passed!{Colors.NC}")
    return True

def main():
    """Run tag parsing tests"""
    print(f"{Colors.BOLD}Tag Parsing Tests{Colors.NC}")
    print("=" * 50)
    
    try:
        if test_tag_parsing():
            print(f"\n{Colors.GREEN}All tests passed! 🎉{Colors.NC}")
            return 0
        else:
            print(f"\n{Colors.RED}Tests failed 😞{Colors.NC}")
            return 1
    except Exception as e:
        print(f"\n{Colors.RED}Test error: {e}{Colors.NC}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
