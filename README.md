# GitHub Package Management

This repository contains scripts to manage GitHub packages, specifically for listing and deleting container packages with names starting with "sha256:".

## Features

- **List all packages** in a GitHub organization
- **Find sha256 packages** - packages with names starting with "sha256:"
- **Safe deletion** with dry-run mode by default
- **Batch operations** with rate limiting
- **Color-coded output** for better readability
- **Error handling** and retry logic

## Files

- `gh_packages.py` - **Main Python script** for GitHub package management
- `gh_packages.sh` - Bash script alternative
- `test_gh_packages.py` - Test script to verify functionality
- `pyproject.toml` - Python project configuration with uv
- `remove-artifacts.sh` - Script to remove build artifacts

## Installation

This project uses `uv` for Python package management. Install dependencies:

```bash
# Install uv if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install project dependencies
uv sync
```

## Setup

1. **Get a GitHub Personal Access Token** with appropriate permissions:
   - Go to GitHub Settings → Developer settings → Personal access tokens
   - Create a token with `read:packages` and `delete:packages` permissions

2. **Set your token** (choose one method):

   ```bash
   # Method 1: Environment variable (recommended)
   export GITHUB_TOKEN="your_token_here"

   # Method 2: Pass as argument
   uv run python gh_packages.py --token "your_token_here" [command]
   ```

## Usage

### List All Packages

```bash
uv run python gh_packages.py list
```

### List SHA256 Versions for a Specific Package

```bash
uv run python gh_packages.py list-sha256 harmony-simulation
```

### Delete SHA256 Versions (Dry Run)

```bash
# This shows what would be deleted but doesn't actually delete
uv run python gh_packages.py delete harmony-simulation
```

### Actually Delete SHA256 Versions

```bash
# This will prompt for confirmation before deleting
uv run python gh_packages.py delete harmony-simulation --no-dry-run
```

### Custom Organization

```bash
uv run python gh_packages.py --org your-org-name list
```

## Examples

Based on your original curl command:

```bash
curl -L -H "Accept: application/vnd.github+json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.github.com/orgs/arendai/packages/container/harmony-simulation/versions?per_page=100"
```

The Python equivalent would be:

```bash
uv run python gh_packages.py --org arendai list-sha256 harmony-simulation
```

## Safety Features

- **Dry-run by default**: The delete command shows what would be deleted without actually deleting
- **Confirmation prompts**: When using `--no-dry-run`, you'll be asked to confirm
- **Rate limiting**: Includes delays between API calls to respect GitHub's rate limits
- **Error handling**: Graceful handling of API errors and network issues

## Testing

Run the test suite to verify everything works:

```bash
uv run python test_gh_packages.py
```

## API Reference

The script targets packages with names starting with "sha256:" as mentioned in your example:

```json
{
  "id": 460976419,
  "name": "sha256:3626ed25ee001c0b111b93c0b8c154ff71165ecabf40129422455f98d8a207a7",
  "url": "https://api.github.com/orgs/Arendai/packages/container/harmony-simulation/versions/460976419",
  ...
}
```

For more information, see the [GitHub Packages API documentation](https://docs.github.com/en/rest/packages/packages?apiVersion=2022-11-28).
