#!/usr/bin/env python3
"""
Example usage of the GitHubPackageManager class
"""

import os
from gh_packages import GitHubPackageManager, Colors

def example_usage():
    """Example of how to use GitHubPackageManager programmatically"""
    
    # Configuration
    token = os.getenv('GITHUB_TOKEN')
    org = 'arendai'
    package_name = 'harmony-simulation'
    
    if not token:
        print(f"{Colors.RED}Please set GITHUB_TOKEN environment variable{Colors.NC}")
        return
    
    print(f"{Colors.BOLD}GitHub Package Manager - Example Usage{Colors.NC}")
    print("=" * 50)
    
    try:
        # Initialize the package manager
        manager = GitHubPackageManager(token, org)
        print(f"{Colors.GREEN}✓ Package manager initialized{Colors.NC}")
        
        # Example 1: List all packages
        print(f"\n{Colors.BLUE}1. Listing all packages...{Colors.NC}")
        try:
            packages = manager.list_org_packages()
            print(f"Found {len(packages)} packages")
            for pkg in packages[:3]:  # Show first 3
                print(f"  - {pkg['name']}")
            if len(packages) > 3:
                print(f"  ... and {len(packages) - 3} more")
        except Exception as e:
            print(f"{Colors.YELLOW}Could not list packages: {e}{Colors.NC}")
        
        # Example 2: Find sha256 versions for a specific package
        print(f"\n{Colors.BLUE}2. Finding sha256 versions for {package_name}...{Colors.NC}")
        try:
            sha256_versions = manager.find_sha256_versions(package_name)
            if sha256_versions:
                print(f"Found {len(sha256_versions)} sha256 versions:")
                for version in sha256_versions[:5]:  # Show first 5
                    name_preview = version['name'][:60] + "..." if len(version['name']) > 60 else version['name']
                    print(f"  - ID: {version['id']}, Name: {name_preview}")
                if len(sha256_versions) > 5:
                    print(f"  ... and {len(sha256_versions) - 5} more")
            else:
                print("No sha256 versions found")
        except Exception as e:
            print(f"{Colors.YELLOW}Could not find sha256 versions: {e}{Colors.NC}")
        
        # Example 3: Dry run processing (safe)
        print(f"\n{Colors.BLUE}3. Dry run processing (shows what would be deleted)...{Colors.NC}")
        try:
            manager.process_sha256_packages(package_name, dry_run=True)
        except Exception as e:
            print(f"{Colors.YELLOW}Could not process packages: {e}{Colors.NC}")
        
        print(f"\n{Colors.GREEN}Example completed successfully!{Colors.NC}")
        print(f"\n{Colors.YELLOW}Note: To actually delete packages, use:{Colors.NC}")
        print(f"  manager.process_sha256_packages('{package_name}', dry_run=False)")
        
    except Exception as e:
        print(f"{Colors.RED}Error: {e}{Colors.NC}")

if __name__ == '__main__':
    example_usage()
