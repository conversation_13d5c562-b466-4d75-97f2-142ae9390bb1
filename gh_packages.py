import os
import requests
from datetime import datetime, timedelta
import argparse

# Function to list all packages in the organization with version counts
def list_packages_with_version_counts(session, org_name, package_type):
    print(f"Listing {package_type} packages in organization: {org_name}")
    url = f'https://api.github.com/orgs/{org_name}/packages?package_type={package_type}'
    response = session.get(url)
    response.raise_for_status()
    packages = response.json()

    for package in packages:
        package_name = package['name']
        # versions_url = f'https://api.github.com/orgs/{org_name}/packages/{package_type}/{package_name}/versions'
        # versions_response = session.get(versions_url)
        # versions_response.raise_for_status()
        # versions = versions_response.json()
        # version_count = len(versions)
        versions = list_package_versions(session, org_name, package_type, package_name)
        version_count = len(versions)
        print(f"Package: {package_name} (ID: {package['id']}) - Versions: {version_count}")

# Function to list versions of a specific package
def list_package_versions(session, org_name, package_type, package_name):
    url = f'https://api.github.com/orgs/{org_name}/packages/{package_type}/{package_name}/versions'
    versions = []
    total_size_bytes = 0
    while url:
        response = session.get(url)
        response.raise_for_status()
        versions.extend(response.json())
        url = response.links.get('next', {}).get('url')
    return versions


# Function to delete a specific package version
def delete_package_version(session, org_name, package_type, package_name, version_id):
    url = f'https://api.github.com/orgs/{org_name}/packages/{package_type}/{package_name}/versions/{version_id}'
    response = session.delete(url)
    response.raise_for_status()
    if response.status_code == 204:
        print(f'Successfully deleted version {version_id} of package {package_name}')

# Function to delete package versions older than a specified number of days
def delete_old_versions(session, org_name, package_type, days_threshold):
    cutoff_date = datetime.utcnow() - timedelta(days=days_threshold)
    packages = list_packages_with_version_counts(session, org_name, package_type)
    for package in packages:
        package_name = package['name']
        versions = list_package_versions(session, org_name, package_type, package_name)
        for version in versions:
            updated_at = datetime.strptime(version['updated_at'], '%Y-%m-%dT%H:%M:%SZ')
            if updated_at < cutoff_date:
                delete_package_version(session, org_name, package_type, package_name, version['id'])

def main():
    parser = argparse.ArgumentParser(description='GitHub Package Manager')
    parser.add_argument('--org', required=True, help='GitHub organization name')
    parser.add_argument('--token', default=os.getenv('GITHUB_PAT_ARENDAI'), help='GitHub Personal Access Token (defaults to GITHUB_PAT_ARENDAI environment variable)')
    subparsers = parser.add_subparsers(dest='command', required=True)

    # Subcommand: list packages with version counts
    list_parser = subparsers.add_parser('list', help='List all packages in the organization with version counts')
    list_parser.add_argument('--package-type', default='container', choices=['container', 'npm', 'maven', 'rubygems', 'nuget', 'composer'],
                             help='Type of the packages to list (e.g., container for Docker images)')

    # Subcommand: list versions for a specific package
    list_versions_parser = subparsers.add_parser('list-versions', help='List all versions of a specific package')
    list_versions_parser.add_argument('--package-type', required=True, choices=['container', 'npm', 'maven', 'rubygems', 'nuget', 'composer'],
                                      help='Type of the package (e.g., container for Docker images)')
    list_versions_parser.add_argument('--package-name', required=True, help='Name of the package')

    # Subcommand: delete old package versions
    delete_parser = subparsers.add_parser('delete', help='Delete old package versions')
    delete_parser.add_argument('--package-type', required=True, choices=['container', 'npm', 'maven', 'rubygems', 'nuget', 'composer'],
                               help='Type of the package (e.g., container for Docker images)')
    delete_parser.add_argument('--days', type=int, required=True, help='Delete versions older than this many days')

    args = parser.parse_args()

    if not args.token:
        parser.error('GitHub Personal Access Token must be provided via --token argument or GITHUB_PAT_ARENDAI environment variable.')

    # Set up the session with authentication
    session = requests.Session()
    session.headers.update({
        'Authorization': f'token {args.token}',
        'Accept': 'application/vnd.github+json',
        'X-GitHub-Api-Version': '2022-11-28'
    })

    if args.command == 'list':
        list_packages_with_version_counts(session, args.org, args.package_type)
    elif args.command == 'list-versions':
        list_package_versions(session, args.org, args.package_type, args.package_name)
    elif args.command == 'delete':
        delete_old_versions(session, args.org, args.package_type, args.days)

if __name__ == '__main__':
    main()
