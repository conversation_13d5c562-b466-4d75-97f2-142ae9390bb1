#!/usr/bin/env python3
"""
GitHub Package Management Script
Lists and deletes GitHub container packages with names starting with "sha256:"
"""

import os
import sys
import time
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class Colors:
    """ANSI color codes for terminal output"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    BOLD = '\033[1m'
    NC = '\033[0m'  # No Color


class GitHubPackageManager:
    """GitHub Package Manager for handling container packages"""

    def __init__(self, token: str, org: str, package_type: str = "container"):
        self.token = token
        self.org = org
        self.package_type = package_type
        self.session = self._create_session()

    def _create_session(self) -> requests.Session:
        """Create a requests session with retry strategy and authentication"""
        session = requests.Session()

        # Set up retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Set headers
        session.headers.update({
            'Authorization': f'Bearer {self.token}',
            'Accept': 'application/vnd.github+json',
            'X-GitHub-Api-Version': '2022-11-28'
        })

        return session

    def list_org_packages(self) -> List[Dict[str, Any]]:
        """List all packages for the organization"""
        print(f"{Colors.BLUE}Fetching packages for organization: {self.org}{Colors.NC}")

        url = f'https://api.github.com/orgs/{self.org}/packages'
        params = {'package_type': self.package_type, 'per_page': 100}

        packages = []
        while url:
            response = self.session.get(url, params=params)
            response.raise_for_status()

            page_packages = response.json()
            packages.extend(page_packages)

            # Check for next page
            if 'next' in response.links:
                url = response.links['next']['url']
                params = {}  # URL already contains params
            else:
                url = None

        return packages

    def list_package_versions(self, package_name: str) -> List[Dict[str, Any]]:
        """List all versions for a specific package"""
        print(f"{Colors.BLUE}Fetching versions for package: {package_name}{Colors.NC}")

        url = f'https://api.github.com/orgs/{self.org}/packages/{self.package_type}/{package_name}/versions'
        params = {'per_page': 100}

        versions = []
        while url:
            response = self.session.get(url, params=params)
            response.raise_for_status()

            page_versions = response.json()
            versions.extend(page_versions)

            # Check for next page
            if 'next' in response.links:
                url = response.links['next']['url']
                params = {}  # URL already contains params
            else:
                url = None

        return versions

    def delete_package_version(self, package_name: str, version_id: int) -> bool:
        """Delete a specific package version"""
        print(f"{Colors.YELLOW}Deleting version {version_id} of package {package_name}...{Colors.NC}")

        url = f'https://api.github.com/orgs/{self.org}/packages/{self.package_type}/{package_name}/versions/{version_id}'

        try:
            response = self.session.delete(url)
            response.raise_for_status()

            if response.status_code == 204:
                print(f"{Colors.GREEN}Successfully deleted version {version_id}{Colors.NC}")
                return True
            else:
                print(f"{Colors.RED}Unexpected response code: {response.status_code}{Colors.NC}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"{Colors.RED}Failed to delete version {version_id}: {e}{Colors.NC}")
            return False

    def find_sha256_versions(self, package_name: str, only_untagged: bool = True) -> List[Dict[str, Any]]:
        """Find all versions with names starting with 'sha256:'

        Args:
            package_name: Name of the package to search
            only_untagged: If True, only return versions with no tags (safer for deletion)
        """
        versions = self.list_package_versions(package_name)
        sha256_versions = []

        for version in versions:
            if version['name'].startswith('sha256:'):
                # Check if version has tags
                tags = []
                if 'metadata' in version and 'container' in version['metadata']:
                    tags = version['metadata']['container'].get('tags', [])

                # Add tag information to the version object
                version['tags'] = tags
                version['has_tags'] = len(tags) > 0

                # Filter based on only_untagged parameter
                if only_untagged:
                    if not version['has_tags']:  # Only untagged versions
                        sha256_versions.append(version)
                else:
                    sha256_versions.append(version)  # All sha256 versions

        return sha256_versions

    def process_sha256_packages(self, package_name: str, dry_run: bool = True, only_untagged: bool = True) -> None:
        """Process and optionally delete sha256 packages

        Args:
            package_name: Name of the package to process
            dry_run: If True, only show what would be deleted
            only_untagged: If True, only process untagged versions (much safer)
        """
        print(f"\n{Colors.BLUE}Processing package: {package_name}{Colors.NC}")

        if only_untagged:
            print(f"{Colors.GREEN}SAFETY MODE: Only processing UNTAGGED sha256 versions{Colors.NC}")
        else:
            print(f"{Colors.RED}WARNING: Processing ALL sha256 versions (including tagged ones){Colors.NC}")

        try:
            # Get all sha256 versions first to show summary
            all_sha256_versions = self.find_sha256_versions(package_name, only_untagged=False)
            target_versions = self.find_sha256_versions(package_name, only_untagged=only_untagged)

            if not all_sha256_versions:
                print(f"{Colors.YELLOW}No sha256 versions found for {package_name}{Colors.NC}")
                return

            # Show summary
            tagged_count = len([v for v in all_sha256_versions if v.get('has_tags', False)])
            untagged_count = len(all_sha256_versions) - tagged_count

            print(f"{Colors.BLUE}Summary:{Colors.NC}")
            print(f"  Total sha256 versions: {len(all_sha256_versions)}")
            print(f"  Tagged versions: {tagged_count}")
            print(f"  Untagged versions: {untagged_count}")
            print(f"  Versions to process: {len(target_versions)}")

            if not target_versions:
                print(f"{Colors.YELLOW}No versions match the criteria for processing{Colors.NC}")
                return

            print(f"\n{Colors.YELLOW}Versions to process:{Colors.NC}")

            deleted_count = 0
            for version in target_versions:
                version_id = version['id']
                version_name = version['name']
                created_at = version['created_at']
                tags = version.get('tags', [])

                # Format tags for display
                tags_display = f"Tags: {', '.join(tags)}" if tags else "Tags: (none)"
                name_preview = version_name[:60] + "..." if len(version_name) > 60 else version_name

                print(f"  ID: {version_id}")
                print(f"    Name: {name_preview}")
                print(f"    {tags_display}")
                print(f"    Created: {created_at}")
                print()

                if not dry_run:
                    if self.delete_package_version(package_name, version_id):
                        deleted_count += 1
                    time.sleep(0.5)  # Rate limiting

            if not dry_run:
                print(f"{Colors.GREEN}Successfully deleted {deleted_count}/{len(target_versions)} versions{Colors.NC}")
            else:
                print(f"{Colors.YELLOW}DRY RUN: Would delete {len(target_versions)} versions{Colors.NC}")

        except requests.exceptions.RequestException as e:
            print(f"{Colors.RED}Error processing package {package_name}: {e}{Colors.NC}")

    def list_all_packages(self) -> None:
        """List all packages in the organization"""
        try:
            packages = self.list_org_packages()
            print(f"\n{Colors.BLUE}Found {len(packages)} {self.package_type} packages:{Colors.NC}")

            for package in packages:
                name = package['name']
                package_type = package.get('package_type', 'unknown')
                created_at = package['created_at']
                print(f"Package: {Colors.GREEN}{name}{Colors.NC}, Type: {package_type}, Created: {created_at}")

        except requests.exceptions.RequestException as e:
            print(f"{Colors.RED}Error listing packages: {e}{Colors.NC}")
            sys.exit(1)

def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(
        description='GitHub Package Management Script - Lists and deletes container packages with sha256 names',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --org arendai list                                    # List all packages
  %(prog)s --org arendai list-sha256 harmony-simulation         # List UNTAGGED sha256 versions (safe)
  %(prog)s --org arendai list-sha256 harmony-simulation --include-tagged  # List ALL sha256 versions
  %(prog)s --org arendai delete harmony-simulation              # Dry run delete of UNTAGGED sha256 versions
  %(prog)s --org arendai delete harmony-simulation --no-dry-run # Actually delete UNTAGGED sha256 versions
  %(prog)s --org arendai delete harmony-simulation --include-tagged --no-dry-run  # DANGER: Delete ALL sha256 versions
        """
    )

    # Global arguments
    parser.add_argument('--org', default='arendai', help='GitHub organization name (default: arendai)')
    parser.add_argument('--token',
                       default=os.getenv('GITHUB_TOKEN'),
                       help='GitHub Personal Access Token (set GITHUB_TOKEN env var or use this argument)')
    parser.add_argument('--package-type', default='container',
                       choices=['container', 'npm', 'maven', 'rubygems', 'nuget', 'composer'],
                       help='Type of packages to manage (default: container)')

    # Subcommands
    subparsers = parser.add_subparsers(dest='command', required=True, help='Available commands')

    # List all packages
    subparsers.add_parser('list', help='List all packages in the organization')

    # List sha256 versions for a specific package
    list_sha256_parser = subparsers.add_parser('list-sha256', help='List sha256 versions for a specific package')
    list_sha256_parser.add_argument('package_name', help='Name of the package to check for sha256 versions')
    list_sha256_parser.add_argument('--include-tagged', action='store_true',
                                   help='Include tagged versions in the list (default: only untagged)')

    # Delete sha256 versions
    delete_parser = subparsers.add_parser('delete', help='Delete sha256 versions from a package')
    delete_parser.add_argument('package_name', help='Name of the package to delete sha256 versions from')
    delete_parser.add_argument('--no-dry-run', action='store_true',
                              help='Actually delete packages (default is dry-run mode)')
    delete_parser.add_argument('--include-tagged', action='store_true',
                              help='DANGEROUS: Include tagged versions for deletion (default: only untagged)')

    args = parser.parse_args()

    # Validate token
    if not args.token:
        print(f"{Colors.RED}Error: GitHub Personal Access Token is required{Colors.NC}")
        print("Set GITHUB_TOKEN environment variable or use --token argument")
        sys.exit(1)

    # Create package manager instance
    try:
        manager = GitHubPackageManager(args.token, args.org, args.package_type)
    except Exception as e:
        print(f"{Colors.RED}Error initializing GitHub Package Manager: {e}{Colors.NC}")
        sys.exit(1)

    # Execute commands
    try:
        if args.command == 'list':
            manager.list_all_packages()

        elif args.command == 'list-sha256':
            only_untagged = not args.include_tagged
            sha256_versions = manager.find_sha256_versions(args.package_name, only_untagged=only_untagged)

            # Also get all versions for summary
            all_sha256_versions = manager.find_sha256_versions(args.package_name, only_untagged=False)

            if all_sha256_versions:
                tagged_count = len([v for v in all_sha256_versions if v.get('has_tags', False)])
                untagged_count = len(all_sha256_versions) - tagged_count

                print(f"\n{Colors.BLUE}Summary for {args.package_name}:{Colors.NC}")
                print(f"  Total sha256 versions: {len(all_sha256_versions)}")
                print(f"  Tagged versions: {tagged_count}")
                print(f"  Untagged versions: {untagged_count}")

                filter_msg = "untagged" if only_untagged else "all"
                print(f"\n{Colors.YELLOW}Showing {filter_msg} sha256 versions ({len(sha256_versions)} versions):{Colors.NC}")

                for version in sha256_versions:
                    tags = version.get('tags', [])
                    tags_display = f"Tags: {', '.join(tags)}" if tags else "Tags: (none)"
                    name_preview = version['name'][:60] + "..." if len(version['name']) > 60 else version['name']

                    print(f"  ID: {version['id']}")
                    print(f"    Name: {name_preview}")
                    print(f"    {tags_display}")
                    print(f"    Created: {version['created_at']}")
                    print()
            else:
                print(f"{Colors.YELLOW}No sha256 versions found in {args.package_name}{Colors.NC}")

        elif args.command == 'delete':
            dry_run = not args.no_dry_run
            only_untagged = not args.include_tagged

            # Extra safety warning for tagged versions
            if args.include_tagged:
                print(f"{Colors.RED}⚠️  DANGER: You've enabled deletion of TAGGED versions!{Colors.NC}")
                print(f"{Colors.RED}   This could delete important releases with semantic versions!{Colors.NC}")

            if not dry_run:
                action_type = "tagged AND untagged" if args.include_tagged else "untagged"
                print(f"{Colors.RED}WARNING: This will permanently delete {action_type} sha256 package versions!{Colors.NC}")
                confirm = input("Are you sure you want to continue? (yes/no): ")
                if confirm.lower() != 'yes':
                    print("Operation cancelled.")
                    sys.exit(0)
            else:
                print(f"{Colors.YELLOW}DRY RUN MODE - No packages will be deleted{Colors.NC}")

            manager.process_sha256_packages(args.package_name, dry_run, only_untagged)

    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}Operation cancelled by user{Colors.NC}")
        sys.exit(0)
    except Exception as e:
        print(f"{Colors.RED}Error: {e}{Colors.NC}")
        sys.exit(1)

if __name__ == '__main__':
    main()
