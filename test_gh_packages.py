#!/usr/bin/env python3
"""
Test script for GitHub Package Manager
"""

import sys
import os

# Add the current directory to the path so we can import gh_packages
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gh_packages import GitHubPackageManager, Colors

def test_structure():
    """Test that the GitHubPackageManager class can be instantiated"""
    print(f"{Colors.BLUE}Testing GitHubPackageManager structure...{Colors.NC}")
    
    # Test with a dummy token
    try:
        manager = GitHubPackageManager("dummy_token", "test_org", "container")
        print(f"{Colors.GREEN}✓ GitHubPackageManager instantiated successfully{Colors.NC}")
        
        # Test that methods exist
        methods_to_test = [
            'list_org_packages',
            'list_package_versions', 
            'delete_package_version',
            'find_sha256_versions',
            'process_sha256_packages',
            'list_all_packages'
        ]
        
        for method_name in methods_to_test:
            if hasattr(manager, method_name):
                print(f"{Colors.GREEN}✓ Method {method_name} exists{Colors.NC}")
            else:
                print(f"{Colors.RED}✗ Method {method_name} missing{Colors.NC}")
                
        return True
        
    except Exception as e:
        print(f"{Colors.RED}✗ Error instantiating GitHubPackageManager: {e}{Colors.NC}")
        return False

def test_cli_help():
    """Test that the CLI help works"""
    print(f"\n{Colors.BLUE}Testing CLI help...{Colors.NC}")
    
    # Import main function
    from gh_packages import main
    
    # Test that main function exists
    if callable(main):
        print(f"{Colors.GREEN}✓ Main function is callable{Colors.NC}")
        return True
    else:
        print(f"{Colors.RED}✗ Main function is not callable{Colors.NC}")
        return False

def main():
    """Run all tests"""
    print(f"{Colors.BOLD}GitHub Package Manager - Structure Tests{Colors.NC}")
    print("=" * 50)
    
    tests = [
        test_structure,
        test_cli_help
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print(f"{Colors.GREEN}All tests passed! 🎉{Colors.NC}")
        return 0
    else:
        print(f"{Colors.RED}Some tests failed 😞{Colors.NC}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
