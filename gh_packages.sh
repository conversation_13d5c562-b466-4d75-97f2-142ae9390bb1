#!/bin/bash

# GitHub Package Management Script
# Lists and deletes GitHub container packages with names starting with "sha256:"

# Configuration
GITHUB_TOKEN="****************************************"
ORG="arendai"
PACKAGE_TYPE="container"
API_VERSION="2022-11-28"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to make GitHub API requests
github_api() {
    local url="$1"
    local method="${2:-GET}"

    curl -s -L \
        -X "$method" \
        -H "Accept: application/vnd.github+json" \
        -H "Authorization: Bearer $GITHUB_TOKEN" \
        -H "X-GitHub-Api-Version: $API_VERSION" \
        "$url"
}

# Function to list all packages for an organization
list_org_packages() {
    echo -e "${BLUE}Fetching packages for organization: $ORG${NC}"
    github_api "https://api.github.com/orgs/$ORG/packages?package_type=$PACKAGE_TYPE&per_page=100"
}

# Function to list package versions for a specific package
list_package_versions() {
    local package_name="$1"
    echo -e "${BLUE}Fetching versions for package: $package_name${NC}"
    github_api "https://api.github.com/orgs/$ORG/packages/$PACKAGE_TYPE/$package_name/versions?per_page=100"
}

# Function to delete a specific package version
delete_package_version() {
    local package_name="$1"
    local version_id="$2"
    echo -e "${YELLOW}Deleting version $version_id of package $package_name...${NC}"

    response=$(github_api "https://api.github.com/orgs/$ORG/packages/$PACKAGE_TYPE/$package_name/versions/$version_id" "DELETE")

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Successfully deleted version $version_id${NC}"
        return 0
    else
        echo -e "${RED}Failed to delete version $version_id${NC}"
        return 1
    fi
}

# Function to find and process sha256 packages
process_sha256_packages() {
    local package_name="$1"
    local dry_run="${2:-true}"

    echo -e "\n${BLUE}Processing package: $package_name${NC}"

    # Get package versions
    versions_json=$(list_package_versions "$package_name")

    # Parse and filter versions with sha256 names
    sha256_versions=$(echo "$versions_json" | jq -r '.[] | select(.name | startswith("sha256:")) | "\(.id)|\(.name)|\(.created_at)"')

    if [ -z "$sha256_versions" ]; then
        echo -e "${YELLOW}No sha256 versions found for $package_name${NC}"
        return
    fi

    echo -e "${YELLOW}Found sha256 versions:${NC}"
    echo "$sha256_versions" | while IFS='|' read -r version_id version_name created_at; do
        echo -e "  ID: $version_id, Name: $version_name, Created: $created_at"

        if [ "$dry_run" = "false" ]; then
            delete_package_version "$package_name" "$version_id"
            sleep 1  # Rate limiting
        fi
    done
}

# Main function
main() {
    local action="${1:-list}"
    local package_name="$2"
    local dry_run="${3:-true}"

    case "$action" in
        "list")
            if [ -n "$package_name" ]; then
                # List versions for specific package
                process_sha256_packages "$package_name" "true"
            else
                # List all packages
                echo -e "${BLUE}Listing all container packages for organization: $ORG${NC}"
                packages_json=$(list_org_packages)
                echo "$packages_json" | jq -r '.[] | "\(.name)|\(.package_type)|\(.created_at)"' | while IFS='|' read -r name type created; do
                    echo -e "Package: ${GREEN}$name${NC}, Type: $type, Created: $created"
                done
            fi
            ;;
        "delete")
            if [ -z "$package_name" ]; then
                echo -e "${RED}Error: Package name is required for delete operation${NC}"
                echo "Usage: $0 delete <package_name> [dry_run]"
                exit 1
            fi

            if [ "$dry_run" = "false" ]; then
                echo -e "${RED}WARNING: This will permanently delete sha256 package versions!${NC}"
                read -p "Are you sure you want to continue? (yes/no): " confirm
                if [ "$confirm" != "yes" ]; then
                    echo "Operation cancelled."
                    exit 0
                fi
            else
                echo -e "${YELLOW}DRY RUN MODE - No packages will be deleted${NC}"
            fi

            process_sha256_packages "$package_name" "$dry_run"
            ;;
        "help"|"-h"|"--help")
            echo "GitHub Package Management Script"
            echo ""
            echo "Usage:"
            echo "  $0 list                           # List all packages"
            echo "  $0 list <package_name>           # List sha256 versions for specific package"
            echo "  $0 delete <package_name>         # Dry run delete of sha256 versions"
            echo "  $0 delete <package_name> false   # Actually delete sha256 versions"
            echo ""
            echo "Examples:"
            echo "  $0 list"
            echo "  $0 list harmony-simulation"
            echo "  $0 delete harmony-simulation"
            echo "  $0 delete harmony-simulation false"
            ;;
        *)
            echo -e "${RED}Unknown action: $action${NC}"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}Error: jq is required but not installed.${NC}"
    echo "Please install jq: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    exit 1
fi

# Run main function with all arguments
main "$@"
